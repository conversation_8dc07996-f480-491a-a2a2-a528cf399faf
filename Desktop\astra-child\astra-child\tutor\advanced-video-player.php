<?php
if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class MemberoAdvancedVideoPlayer {
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Enfileirar scripts e estilos
        add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
        
        // Adicionar configurações do player
        add_action('wp_footer', [$this, 'add_player_config']);
        
        // Hook para adicionar controles personalizados
        add_action('tutor_lesson/single/after/video/html5', [$this, 'add_advanced_controls']);
        add_action('tutor_lesson/single/after/video/external_url', [$this, 'add_advanced_controls']);
        
        // AJAX para salvar configurações do player
        add_action('wp_ajax_save_player_settings', [$this, 'save_player_settings']);
        add_action('wp_ajax_get_player_settings', [$this, 'get_player_settings']);
        
        // AJAX para detecção de gravação de tela
        add_action('wp_ajax_report_screen_recording', [$this, 'handle_screen_recording_detection']);
    }
    
    public function enqueue_assets() {
        if (!is_singular('lesson')) {
            return;
        }
        
        // Plyr.js para player avançado
        wp_enqueue_style(
            'plyr-css',
            'https://cdn.plyr.io/3.7.8/plyr.css',
            [],
            '3.7.8'
        );
        
        wp_enqueue_script(
            'plyr-js',
            'https://cdn.plyr.io/3.7.8/plyr.polyfilled.js',
            [],
            '3.7.8',
            true
        );
        
        // CSS personalizado do player
        wp_enqueue_style(
            'membero-advanced-player',
            get_stylesheet_directory_uri() . '/assets/css/advanced-video-player.css',
            ['plyr-css'],
            filemtime(get_stylesheet_directory() . '/assets/css/advanced-video-player.css')
        );
        
        // JavaScript do player avançado
        wp_enqueue_script(
            'membero-advanced-player',
            get_stylesheet_directory_uri() . '/assets/js/advanced-video-player.js',
            ['jquery', 'plyr-js'],
            filemtime(get_stylesheet_directory() . '/assets/js/advanced-video-player.js'),
            true
        );
        
        // Localizar dados para o JavaScript
        $this->localize_player_data();
    }
    
    private function localize_player_data() {
        global $post;
        $course_id = tutor_utils()->get_course_id_by_lesson($post->ID);
        $user_id = get_current_user_id();
        
        $player_data = [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('membero_player_nonce'),
            'lessonId' => $post->ID,
            'courseId' => $course_id,
            'userId' => $user_id,
            'userSettings' => $this->get_user_player_settings($user_id),
            'chapters' => $this->get_lesson_chapters($post->ID),
            'annotations' => $this->get_user_annotations($user_id, $post->ID),
            'screenRecordingDetection' => get_option('membero_screen_recording_detection', true),
            'strings' => [
                'speedLabel' => 'Velocidade',
                'chaptersLabel' => 'Capítulos',
                'annotationsLabel' => 'Anotações',
                'theaterMode' => 'Modo Teatro',
                'addAnnotation' => 'Adicionar Anotação',
                'saveAnnotation' => 'Salvar',
                'cancelAnnotation' => 'Cancelar',
                'screenRecordingDetected' => 'Gravação de tela detectada! O vídeo será pausado.',
                'multipleTabsDetected' => 'Múltiplas abas detectadas. Mantenha apenas uma aba aberta.'
            ]
        ];
        
        wp_localize_script('membero-advanced-player', 'memberoPlayer', $player_data);
    }
    
    public function add_advanced_controls() {
        ?>
        <div id="membero-player-controls" class="membero-player-controls">
            <div class="membero-controls-row">
                <button id="membero-theater-mode" class="membero-control-btn" title="Modo Teatro">
                    <i class="fas fa-expand"></i>
                </button>
                <button id="membero-chapters-toggle" class="membero-control-btn" title="Capítulos">
                    <i class="fas fa-list"></i>
                </button>
                <button id="membero-annotations-toggle" class="membero-control-btn" title="Anotações">
                    <i class="fas fa-sticky-note"></i>
                </button>
                <div class="membero-speed-control">
                    <label for="membero-speed-select">Velocidade:</label>
                    <select id="membero-speed-select">
                        <option value="0.5">0.5x</option>
                        <option value="0.75">0.75x</option>
                        <option value="1" selected>1x</option>
                        <option value="1.25">1.25x</option>
                        <option value="1.5">1.5x</option>
                        <option value="2">2x</option>
                    </select>
                </div>
            </div>
            
            <!-- Painel de Capítulos -->
            <div id="membero-chapters-panel" class="membero-panel" style="display: none;">
                <h4>Capítulos</h4>
                <div id="membero-chapters-list"></div>
            </div>
            
            <!-- Painel de Anotações -->
            <div id="membero-annotations-panel" class="membero-panel" style="display: none;">
                <h4>Minhas Anotações</h4>
                <button id="membero-add-annotation" class="membero-btn-primary">
                    <i class="fas fa-plus"></i> Adicionar Anotação
                </button>
                <div id="membero-annotations-list"></div>
            </div>
        </div>
        
        <!-- Modal para adicionar anotação -->
        <div id="membero-annotation-modal" class="membero-modal" style="display: none;">
            <div class="membero-modal-content">
                <h4>Adicionar Anotação</h4>
                <p>Tempo: <span id="membero-annotation-time"></span></p>
                <textarea id="membero-annotation-text" placeholder="Digite sua anotação..."></textarea>
                <div class="membero-modal-actions">
                    <button id="membero-save-annotation" class="membero-btn-primary">Salvar</button>
                    <button id="membero-cancel-annotation" class="membero-btn-secondary">Cancelar</button>
                </div>
            </div>
        </div>
        
        <!-- Overlay de detecção de gravação -->
        <div id="membero-recording-warning" class="membero-warning-overlay" style="display: none;">
            <div class="membero-warning-content">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Gravação de Tela Detectada</h3>
                <p>Por favor, pare a gravação para continuar assistindo ao vídeo.</p>
            </div>
        </div>
        <?php
    }
    
    public function add_player_config() {
        if (!is_singular('lesson')) {
            return;
        }
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof MemberoAdvancedPlayer !== 'undefined') {
                window.memberoPlayerInstance = new MemberoAdvancedPlayer();
            }
        });
        </script>
        <?php
    }
    
    private function get_user_player_settings($user_id) {
        $default_settings = [
            'speed' => 1,
            'volume' => 1,
            'quality' => 'auto',
            'theater_mode' => false
        ];
        
        $saved_settings = get_user_meta($user_id, 'membero_player_settings', true);
        return wp_parse_args($saved_settings, $default_settings);
    }
    
    private function get_lesson_chapters($lesson_id) {
        $chapters = get_post_meta($lesson_id, '_membero_video_chapters', true);
        return $chapters ? $chapters : [];
    }
    
    private function get_user_annotations($user_id, $lesson_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'membero_video_annotations';
        
        $annotations = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE user_id = %d AND lesson_id = %d ORDER BY time_seconds ASC",
            $user_id,
            $lesson_id
        ));
        
        return $annotations ? $annotations : [];
    }
    
    public function save_player_settings() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Usuário não autenticado');
        }
        
        $settings = [
            'speed' => floatval($_POST['speed'] ?? 1),
            'volume' => floatval($_POST['volume'] ?? 1),
            'quality' => sanitize_text_field($_POST['quality'] ?? 'auto'),
            'theater_mode' => boolval($_POST['theater_mode'] ?? false)
        ];
        
        update_user_meta($user_id, 'membero_player_settings', $settings);
        
        wp_send_json_success('Configurações salvas');
    }
    
    public function get_player_settings() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Usuário não autenticado');
        }
        
        $settings = $this->get_user_player_settings($user_id);
        wp_send_json_success($settings);
    }
    
    public function handle_screen_recording_detection() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $lesson_id = intval($_POST['lesson_id'] ?? 0);
        $detection_type = sanitize_text_field($_POST['detection_type'] ?? '');
        
        if (!$user_id || !$lesson_id) {
            wp_send_json_error('Dados inválidos');
        }
        
        // Log da detecção
        $log_data = [
            'user_id' => $user_id,
            'lesson_id' => $lesson_id,
            'detection_type' => $detection_type,
            'timestamp' => current_time('mysql'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        // Salvar no log de segurança
        $this->log_security_event('screen_recording_detected', $log_data);
        
        // Notificar administradores se configurado
        if (get_option('membero_notify_admin_recording', true)) {
            $this->notify_admin_recording_detection($log_data);
        }
        
        wp_send_json_success('Detecção registrada');
    }
    
    private function log_security_event($event_type, $data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'membero_security_logs';
        
        $wpdb->insert(
            $table_name,
            [
                'event_type' => $event_type,
                'user_id' => $data['user_id'],
                'lesson_id' => $data['lesson_id'],
                'event_data' => json_encode($data),
                'created_at' => current_time('mysql')
            ],
            ['%s', '%d', '%d', '%s', '%s']
        );
    }
    
    private function notify_admin_recording_detection($data) {
        $user = get_user_by('id', $data['user_id']);
        $lesson = get_post($data['lesson_id']);
        
        $subject = 'Detecção de Gravação de Tela - ' . get_bloginfo('name');
        $message = sprintf(
            "Detecção de gravação de tela:\n\nUsuário: %s (%s)\nLição: %s\nTipo: %s\nData/Hora: %s\nIP: %s",
            $user->display_name,
            $user->user_email,
            $lesson->post_title,
            $data['detection_type'],
            $data['timestamp'],
            $data['ip_address']
        );
        
        $admin_email = get_option('admin_email');
        wp_mail($admin_email, $subject, $message);
    }
}

// Inicializar a classe
MemberoAdvancedVideoPlayer::get_instance();
