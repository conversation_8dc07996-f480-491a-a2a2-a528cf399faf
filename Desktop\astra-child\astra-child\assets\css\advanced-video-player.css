/* Membero Advanced Video Player Styles */

/* Container principal do player */
.tutor-video-player {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

/* Modo teatro */
.membero-theater-mode {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 999999 !important;
    background: #000 !important;
    border-radius: 0 !important;
}

.membero-theater-mode .plyr {
    width: 100% !important;
    height: 100% !important;
}

/* Controles personalizados */
.membero-player-controls {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 15px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.membero-controls-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.membero-control-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.membero-control-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.membero-control-btn.active {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.4);
}

.membero-speed-control {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-size: 14px;
}

.membero-speed-control label {
    font-weight: 500;
}

.membero-speed-control select {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.membero-speed-control select option {
    background: #333;
    color: white;
}

/* Painéis laterais */
.membero-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.membero-panel h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
}

/* Lista de capítulos */
.membero-chapters-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.membero-chapter-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.membero-chapter-item:hover {
    background: #e9ecef;
    border-left-color: #667eea;
    transform: translateX(4px);
}

.membero-chapter-item.active {
    background: #667eea;
    color: white;
    border-left-color: #5a6fd8;
}

.membero-chapter-time {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 10px;
    min-width: 50px;
    text-align: center;
}

.membero-chapter-item.active .membero-chapter-time {
    background: rgba(255,255,255,0.2);
    color: white;
}

.membero-chapter-title {
    flex: 1;
    font-weight: 500;
}

/* Lista de anotações */
.membero-annotations-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
}

.membero-annotation-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
}

.membero-annotation-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.membero-annotation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.membero-annotation-time {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}

.membero-annotation-actions {
    display: flex;
    gap: 5px;
}

.membero-annotation-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: color 0.3s ease;
}

.membero-annotation-btn:hover {
    color: #495057;
}

.membero-annotation-btn.delete:hover {
    color: #dc3545;
}

.membero-annotation-text {
    color: #495057;
    line-height: 1.5;
    font-size: 14px;
}

/* Botões */
.membero-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.membero-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.membero-btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.membero-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Modal */
.membero-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.membero-modal-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.membero-modal-content h4 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.membero-modal-content textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    margin: 10px 0;
    transition: border-color 0.3s ease;
}

.membero-modal-content textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.membero-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Overlay de aviso */
.membero-warning-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(220, 53, 69, 0.95);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.membero-warning-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.membero-warning-content i {
    font-size: 48px;
    color: #dc3545;
    margin-bottom: 20px;
}

.membero-warning-content h3 {
    color: #dc3545;
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
}

.membero-warning-content p {
    color: #6c757d;
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
    .membero-controls-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .membero-control-btn {
        justify-content: center;
    }
    
    .membero-speed-control {
        justify-content: center;
    }
    
    .membero-modal-content {
        margin: 20px;
        padding: 20px;
    }
    
    .membero-warning-content {
        margin: 20px;
        padding: 30px;
    }
}

/* Integração com Plyr */
.plyr {
    border-radius: 8px 8px 0 0;
}

.plyr--video {
    background: #000;
}

.plyr__controls {
    background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.8) 100%);
}

/* Animações suaves */
.membero-panel {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estados de loading */
.membero-loading {
    opacity: 0.6;
    pointer-events: none;
}

.membero-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
