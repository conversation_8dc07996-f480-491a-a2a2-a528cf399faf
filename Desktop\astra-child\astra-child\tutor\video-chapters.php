<?php
if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class MemberoVideoChapters {
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Meta box para adicionar capítulos nas lições
        add_action('add_meta_boxes', [$this, 'add_chapters_meta_box']);
        add_action('save_post', [$this, 'save_chapters_meta_box']);
        
        // AJAX handlers
        add_action('wp_ajax_save_video_chapters', [$this, 'save_chapters_ajax']);
        add_action('wp_ajax_get_video_chapters', [$this, 'get_chapters_ajax']);
        
        // Enqueue scripts para admin
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        
        // Adicionar colunas na lista de lições
        add_filter('manage_lesson_posts_columns', [$this, 'add_chapters_column']);
        add_action('manage_lesson_posts_custom_column', [$this, 'display_chapters_column'], 10, 2);
    }
    
    public function add_chapters_meta_box() {
        add_meta_box(
            'membero_video_chapters',
            'Capítulos do Vídeo',
            [$this, 'render_chapters_meta_box'],
            'lesson',
            'normal',
            'high'
        );
    }
    
    public function render_chapters_meta_box($post) {
        wp_nonce_field('membero_chapters_nonce', 'membero_chapters_nonce_field');
        
        $chapters = get_post_meta($post->ID, '_membero_video_chapters', true);
        if (!is_array($chapters)) {
            $chapters = [];
        }
        
        ?>
        <div id="membero-chapters-container">
            <div class="membero-chapters-header">
                <h4>Configurar Capítulos do Vídeo</h4>
                <p>Adicione marcadores de capítulos para facilitar a navegação no vídeo.</p>
                <button type="button" id="add-chapter" class="button button-primary">
                    <span class="dashicons dashicons-plus"></span> Adicionar Capítulo
                </button>
            </div>
            
            <div id="chapters-list" class="membero-chapters-list">
                <?php if (empty($chapters)): ?>
                    <div class="no-chapters">
                        <p>Nenhum capítulo configurado. Clique em "Adicionar Capítulo" para começar.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($chapters as $index => $chapter): ?>
                        <?php $this->render_chapter_item($index, $chapter); ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <div class="membero-chapters-actions">
                <button type="button" id="auto-generate-chapters" class="button">
                    <span class="dashicons dashicons-admin-tools"></span> Gerar Capítulos Automaticamente
                </button>
                <button type="button" id="import-chapters" class="button">
                    <span class="dashicons dashicons-upload"></span> Importar de Arquivo
                </button>
                <button type="button" id="export-chapters" class="button">
                    <span class="dashicons dashicons-download"></span> Exportar Capítulos
                </button>
            </div>
        </div>
        
        <!-- Template para novo capítulo -->
        <script type="text/template" id="chapter-template">
            <?php $this->render_chapter_item('{{INDEX}}', ['time' => '', 'title' => '', 'description' => '']); ?>
        </script>
        
        <style>
        .membero-chapters-header {
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .membero-chapters-list {
            min-height: 100px;
        }
        
        .no-chapters {
            text-align: center;
            padding: 40px;
            background: #f9f9f9;
            border: 2px dashed #ddd;
            border-radius: 8px;
            color: #666;
        }
        
        .chapter-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }
        
        .chapter-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chapter-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .chapter-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .chapter-time-input {
            width: 120px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .chapter-title-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .chapter-actions {
            display: flex;
            gap: 5px;
        }
        
        .chapter-description {
            margin-top: 10px;
        }
        
        .chapter-description textarea {
            width: 100%;
            min-height: 60px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        
        .membero-chapters-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
            display: flex;
            gap: 10px;
        }
        
        .sortable-placeholder {
            background: #f0f0f0;
            border: 2px dashed #ccc;
            height: 100px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .chapter-item.ui-sortable-helper {
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transform: rotate(2deg);
        }
        </style>
        <?php
    }
    
    private function render_chapter_item($index, $chapter) {
        $time = $chapter['time'] ?? '';
        $title = $chapter['title'] ?? '';
        $description = $chapter['description'] ?? '';
        $chapter_number = is_numeric($index) ? $index + 1 : '{{NUMBER}}';
        
        ?>
        <div class="chapter-item" data-index="<?php echo esc_attr($index); ?>">
            <div class="chapter-header">
                <div class="chapter-number"><?php echo $chapter_number; ?></div>
                <input type="text" 
                       name="chapters[<?php echo esc_attr($index); ?>][time]" 
                       value="<?php echo esc_attr($time); ?>"
                       placeholder="00:00"
                       class="chapter-time-input"
                       pattern="^([0-9]{1,2}:)?[0-9]{1,2}:[0-9]{2}$"
                       title="Formato: MM:SS ou HH:MM:SS">
                <input type="text" 
                       name="chapters[<?php echo esc_attr($index); ?>][title]" 
                       value="<?php echo esc_attr($title); ?>"
                       placeholder="Título do capítulo"
                       class="chapter-title-input"
                       required>
                <div class="chapter-actions">
                    <button type="button" class="button button-small move-up" title="Mover para cima">
                        <span class="dashicons dashicons-arrow-up-alt2"></span>
                    </button>
                    <button type="button" class="button button-small move-down" title="Mover para baixo">
                        <span class="dashicons dashicons-arrow-down-alt2"></span>
                    </button>
                    <button type="button" class="button button-small button-link-delete remove-chapter" title="Remover capítulo">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            </div>
            <div class="chapter-description">
                <textarea name="chapters[<?php echo esc_attr($index); ?>][description]" 
                          placeholder="Descrição do capítulo (opcional)"><?php echo esc_textarea($description); ?></textarea>
            </div>
        </div>
        <?php
    }
    
    public function save_chapters_meta_box($post_id) {
        // Verificar nonce
        if (!isset($_POST['membero_chapters_nonce_field']) || 
            !wp_verify_nonce($_POST['membero_chapters_nonce_field'], 'membero_chapters_nonce')) {
            return;
        }
        
        // Verificar autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Verificar permissões
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Verificar se é uma lição
        if (get_post_type($post_id) !== 'lesson') {
            return;
        }
        
        $chapters = [];
        
        if (isset($_POST['chapters']) && is_array($_POST['chapters'])) {
            foreach ($_POST['chapters'] as $chapter_data) {
                $time = sanitize_text_field($chapter_data['time'] ?? '');
                $title = sanitize_text_field($chapter_data['title'] ?? '');
                $description = sanitize_textarea_field($chapter_data['description'] ?? '');
                
                if ($time && $title) {
                    // Converter tempo para segundos
                    $time_seconds = $this->time_to_seconds($time);
                    
                    $chapters[] = [
                        'time' => $time_seconds,
                        'title' => $title,
                        'description' => $description,
                        'formatted_time' => $time
                    ];
                }
            }
            
            // Ordenar por tempo
            usort($chapters, function($a, $b) {
                return $a['time'] <=> $b['time'];
            });
        }
        
        update_post_meta($post_id, '_membero_video_chapters', $chapters);
    }
    
    public function enqueue_admin_scripts($hook) {
        global $post_type;
        
        if ($post_type !== 'lesson' || !in_array($hook, ['post.php', 'post-new.php'])) {
            return;
        }
        
        wp_enqueue_script('jquery-ui-sortable');
        
        wp_enqueue_script(
            'membero-chapters-admin',
            get_stylesheet_directory_uri() . '/assets/js/chapters-admin.js',
            ['jquery', 'jquery-ui-sortable'],
            filemtime(get_stylesheet_directory() . '/assets/js/chapters-admin.js'),
            true
        );
        
        wp_localize_script('membero-chapters-admin', 'memberoChapters', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('membero_chapters_nonce'),
            'strings' => [
                'confirmDelete' => 'Tem certeza que deseja remover este capítulo?',
                'invalidTime' => 'Formato de tempo inválido. Use MM:SS ou HH:MM:SS',
                'duplicateTime' => 'Já existe um capítulo neste tempo',
                'autoGenerateConfirm' => 'Isso substituirá todos os capítulos existentes. Continuar?'
            ]
        ]);
    }
    
    public function save_chapters_ajax() {
        check_ajax_referer('membero_chapters_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permissão negada');
        }
        
        $lesson_id = intval($_POST['lesson_id'] ?? 0);
        $chapters = $_POST['chapters'] ?? [];
        
        if (!$lesson_id) {
            wp_send_json_error('ID da lição não fornecido');
        }
        
        $processed_chapters = [];
        
        foreach ($chapters as $chapter) {
            $time = sanitize_text_field($chapter['time'] ?? '');
            $title = sanitize_text_field($chapter['title'] ?? '');
            $description = sanitize_textarea_field($chapter['description'] ?? '');
            
            if ($time && $title) {
                $time_seconds = $this->time_to_seconds($time);
                
                $processed_chapters[] = [
                    'time' => $time_seconds,
                    'title' => $title,
                    'description' => $description,
                    'formatted_time' => $time
                ];
            }
        }
        
        // Ordenar por tempo
        usort($processed_chapters, function($a, $b) {
            return $a['time'] <=> $b['time'];
        });
        
        update_post_meta($lesson_id, '_membero_video_chapters', $processed_chapters);
        
        wp_send_json_success([
            'message' => 'Capítulos salvos com sucesso',
            'chapters' => $processed_chapters
        ]);
    }
    
    public function get_chapters_ajax() {
        check_ajax_referer('membero_chapters_nonce', 'nonce');
        
        $lesson_id = intval($_POST['lesson_id'] ?? 0);
        
        if (!$lesson_id) {
            wp_send_json_error('ID da lição não fornecido');
        }
        
        $chapters = get_post_meta($lesson_id, '_membero_video_chapters', true);
        
        if (!is_array($chapters)) {
            $chapters = [];
        }
        
        wp_send_json_success($chapters);
    }
    
    public function add_chapters_column($columns) {
        $columns['video_chapters'] = 'Capítulos';
        return $columns;
    }
    
    public function display_chapters_column($column, $post_id) {
        if ($column === 'video_chapters') {
            $chapters = get_post_meta($post_id, '_membero_video_chapters', true);
            
            if (is_array($chapters) && !empty($chapters)) {
                echo '<span class="dashicons dashicons-video-alt3"></span> ' . count($chapters) . ' capítulos';
            } else {
                echo '<span style="color: #999;">Sem capítulos</span>';
            }
        }
    }
    
    private function time_to_seconds($time_string) {
        $parts = explode(':', $time_string);
        $seconds = 0;
        
        if (count($parts) === 2) {
            // MM:SS
            $seconds = (intval($parts[0]) * 60) + intval($parts[1]);
        } elseif (count($parts) === 3) {
            // HH:MM:SS
            $seconds = (intval($parts[0]) * 3600) + (intval($parts[1]) * 60) + intval($parts[2]);
        }
        
        return $seconds;
    }
    
    private function seconds_to_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;
        
        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $secs);
        }
        return sprintf('%d:%02d', $minutes, $secs);
    }
    
    // Método público para obter capítulos (usado pelo player)
    public function get_lesson_chapters($lesson_id) {
        $chapters = get_post_meta($lesson_id, '_membero_video_chapters', true);
        
        if (!is_array($chapters)) {
            return [];
        }
        
        return $chapters;
    }
    
    // Método para auto-gerar capítulos baseado na duração do vídeo
    public function auto_generate_chapters($lesson_id, $duration_seconds, $chapter_count = 5) {
        if ($duration_seconds <= 0 || $chapter_count <= 0) {
            return [];
        }
        
        $interval = $duration_seconds / $chapter_count;
        $chapters = [];
        
        for ($i = 0; $i < $chapter_count; $i++) {
            $time = $i * $interval;
            $chapter_number = $i + 1;
            
            $chapters[] = [
                'time' => $time,
                'title' => "Capítulo {$chapter_number}",
                'description' => '',
                'formatted_time' => $this->seconds_to_time($time)
            ];
        }
        
        return $chapters;
    }
}

// Inicializar a classe
MemberoVideoChapters::get_instance();
