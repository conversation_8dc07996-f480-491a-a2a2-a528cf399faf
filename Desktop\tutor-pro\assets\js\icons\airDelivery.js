"use strict";(self["webpackChunktutor"]=self["webpackChunktutor"]||[]).push([[3395],{90730:(a,l,h)=>{h.r(l);h.d(l,{default:()=>t});const t={icon:'<path d="M33.4 18.376a.75.75 0 0 0-1.022-.279l-13.32 7.614 3.84-6.585a.75.75 0 0 0-1.296-.75L17 26.262l-4.602-7.886a.75.75 0 0 0-1.296.75l3.839 6.582L1.625 18.1a.748.748 0 0 0-1.039.27.75.75 0 0 0 .295 1.032l15.75 9a.75.75 0 0 0 .75 0l15.744-9a.75.75 0 0 0 .276-1.026Z" fill="#2946ED"/><path d="M28.25 27H5.75a.75.75 0 0 0-.75.75v19.5c0 .414.336.75.75.75h22.5a.75.75 0 0 0 .75-.75v-19.5a.75.75 0 0 0-.75-.75Z" fill="#E6ECFF"/><path d="M13.25 27v7.5a.75.75 0 0 0 .75.75h6a.75.75 0 0 0 .75-.75V27h-7.5ZM15.5 43.5H7.25a.75.75 0 1 1 0-1.5h8.25a.75.75 0 1 1 0 1.5ZM11.75 46.5h-4.5a.75.75 0 1 1 0-1.5h4.5a.75.75 0 1 1 0 1.5ZM17 0A16.519 16.519 0 0 0 .5 16.5v2.25a.75.75 0 0 0 1.342.46 5.902 5.902 0 0 1 9.316 0 .776.776 0 0 0 1.184 0 5.902 5.902 0 0 1 9.316 0 .777.777 0 0 0 1.184 0 5.9 5.9 0 0 1 9.316 0 .75.75 0 0 0 1.342-.46V16.5A16.519 16.519 0 0 0 17 0Z" fill="#4294FF"/><path d="m11.75 18.752.322.667a.783.783 0 0 1-.914-.21 5.909 5.909 0 0 0-9.316 0A.75.75 0 0 1 .5 18.752v-2.25A16.525 16.525 0 0 1 14.27.227c-8.07 7.02-2.52 18.525-2.52 18.525ZM33.5 16.502v2.25a.75.75 0 0 1-1.342.457 5.909 5.909 0 0 0-9.315 0 .783.783 0 0 1-.915.21l.322-.667S27.8 7.232 19.723.227A16.532 16.532 0 0 1 33.5 16.502Z" fill="#376CFB"/>',viewBox:"0 0 34 48"}}}]);