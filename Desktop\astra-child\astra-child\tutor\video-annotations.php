<?php
if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class MemberoVideoAnnotations {
    private static $instance = null;
    private $table_name;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'membero_video_annotations';
        
        $this->init_hooks();
        $this->create_tables();
    }
    
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_save_video_annotation', [$this, 'save_annotation']);
        add_action('wp_ajax_delete_video_annotation', [$this, 'delete_annotation']);
        add_action('wp_ajax_get_video_annotations', [$this, 'get_annotations']);
        add_action('wp_ajax_update_video_annotation', [$this, 'update_annotation']);
        
        // Admin hooks
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('wp_ajax_export_annotations', [$this, 'export_annotations']);
    }
    
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            lesson_id bigint(20) NOT NULL,
            course_id bigint(20) NOT NULL,
            time_seconds decimal(10,2) NOT NULL,
            text longtext NOT NULL,
            is_private tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_lesson (user_id, lesson_id),
            KEY lesson_time (lesson_id, time_seconds),
            KEY course_user (course_id, user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Criar tabela de logs de segurança se não existir
        $this->create_security_logs_table();
    }
    
    private function create_security_logs_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'membero_security_logs';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            event_type varchar(50) NOT NULL,
            user_id bigint(20) NOT NULL,
            lesson_id bigint(20) DEFAULT NULL,
            course_id bigint(20) DEFAULT NULL,
            event_data longtext,
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    public function save_annotation() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Usuário não autenticado');
        }
        
        $lesson_id = intval($_POST['lesson_id'] ?? 0);
        $time_seconds = floatval($_POST['time_seconds'] ?? 0);
        $text = sanitize_textarea_field($_POST['text'] ?? '');
        
        if (!$lesson_id || !$text) {
            wp_send_json_error('Dados obrigatórios não fornecidos');
        }
        
        // Verificar se o usuário tem acesso à lição
        if (!tutor_utils()->has_enrolled_content_access('lesson', $lesson_id)) {
            wp_send_json_error('Acesso negado');
        }
        
        $course_id = tutor_utils()->get_course_id_by_lesson($lesson_id);
        
        global $wpdb;
        
        $result = $wpdb->insert(
            $this->table_name,
            [
                'user_id' => $user_id,
                'lesson_id' => $lesson_id,
                'course_id' => $course_id,
                'time_seconds' => $time_seconds,
                'text' => $text,
                'is_private' => 1
            ],
            ['%d', '%d', '%d', '%f', '%s', '%d']
        );
        
        if ($result === false) {
            wp_send_json_error('Erro ao salvar anotação');
        }
        
        $annotation_id = $wpdb->insert_id;
        
        wp_send_json_success([
            'id' => $annotation_id,
            'message' => 'Anotação salva com sucesso'
        ]);
    }
    
    public function delete_annotation() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Usuário não autenticado');
        }
        
        $annotation_id = intval($_POST['annotation_id'] ?? 0);
        if (!$annotation_id) {
            wp_send_json_error('ID da anotação não fornecido');
        }
        
        global $wpdb;
        
        // Verificar se a anotação pertence ao usuário
        $annotation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE id = %d AND user_id = %d",
            $annotation_id,
            $user_id
        ));
        
        if (!$annotation) {
            wp_send_json_error('Anotação não encontrada ou acesso negado');
        }
        
        $result = $wpdb->delete(
            $this->table_name,
            ['id' => $annotation_id, 'user_id' => $user_id],
            ['%d', '%d']
        );
        
        if ($result === false) {
            wp_send_json_error('Erro ao excluir anotação');
        }
        
        wp_send_json_success('Anotação excluída com sucesso');
    }
    
    public function get_annotations() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Usuário não autenticado');
        }
        
        $lesson_id = intval($_POST['lesson_id'] ?? 0);
        if (!$lesson_id) {
            wp_send_json_error('ID da lição não fornecido');
        }
        
        // Verificar acesso
        if (!tutor_utils()->has_enrolled_content_access('lesson', $lesson_id)) {
            wp_send_json_error('Acesso negado');
        }
        
        global $wpdb;
        
        $annotations = $wpdb->get_results($wpdb->prepare(
            "SELECT id, time_seconds, text, created_at, updated_at 
             FROM {$this->table_name} 
             WHERE user_id = %d AND lesson_id = %d 
             ORDER BY time_seconds ASC",
            $user_id,
            $lesson_id
        ));
        
        wp_send_json_success($annotations);
    }
    
    public function update_annotation() {
        check_ajax_referer('membero_player_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Usuário não autenticado');
        }
        
        $annotation_id = intval($_POST['annotation_id'] ?? 0);
        $text = sanitize_textarea_field($_POST['text'] ?? '');
        
        if (!$annotation_id || !$text) {
            wp_send_json_error('Dados obrigatórios não fornecidos');
        }
        
        global $wpdb;
        
        // Verificar propriedade
        $annotation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE id = %d AND user_id = %d",
            $annotation_id,
            $user_id
        ));
        
        if (!$annotation) {
            wp_send_json_error('Anotação não encontrada ou acesso negado');
        }
        
        $result = $wpdb->update(
            $this->table_name,
            ['text' => $text],
            ['id' => $annotation_id, 'user_id' => $user_id],
            ['%s'],
            ['%d', '%d']
        );
        
        if ($result === false) {
            wp_send_json_error('Erro ao atualizar anotação');
        }
        
        wp_send_json_success('Anotação atualizada com sucesso');
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'tutor',
            'Anotações de Vídeo',
            'Anotações',
            'manage_options',
            'membero-annotations',
            [$this, 'admin_page']
        );
    }
    
    public function admin_page() {
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        // Estatísticas
        $stats = $this->get_annotation_stats();
        
        ?>
        <div class="wrap">
            <h1>Anotações de Vídeo - Membero</h1>
            
            <div class="membero-admin-stats">
                <div class="membero-stat-card">
                    <h3>Total de Anotações</h3>
                    <p class="membero-stat-number"><?php echo number_format($stats['total']); ?></p>
                </div>
                <div class="membero-stat-card">
                    <h3>Usuários Ativos</h3>
                    <p class="membero-stat-number"><?php echo number_format($stats['active_users']); ?></p>
                </div>
                <div class="membero-stat-card">
                    <h3>Lições com Anotações</h3>
                    <p class="membero-stat-number"><?php echo number_format($stats['lessons_with_annotations']); ?></p>
                </div>
            </div>
            
            <div class="membero-admin-actions">
                <button id="export-annotations" class="button button-primary">
                    Exportar Anotações
                </button>
                <button id="clear-old-annotations" class="button button-secondary">
                    Limpar Anotações Antigas (>6 meses)
                </button>
            </div>
            
            <div id="annotations-table-container">
                <?php $this->render_annotations_table(); ?>
            </div>
        </div>
        
        <style>
        .membero-admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .membero-stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .membero-stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        
        .membero-admin-actions {
            margin: 20px 0;
        }
        
        .membero-admin-actions .button {
            margin-right: 10px;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            $('#export-annotations').on('click', function() {
                window.location.href = ajaxurl + '?action=export_annotations&_wpnonce=' + '<?php echo wp_create_nonce('export_annotations'); ?>';
            });
            
            $('#clear-old-annotations').on('click', function() {
                if (confirm('Tem certeza que deseja excluir anotações antigas? Esta ação não pode ser desfeita.')) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'clear_old_annotations',
                            nonce: '<?php echo wp_create_nonce('clear_annotations'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                alert('Anotações antigas removidas com sucesso');
                                location.reload();
                            } else {
                                alert('Erro: ' + response.data);
                            }
                        }
                    });
                }
            });
        });
        </script>
        <?php
    }
    
    private function get_annotation_stats() {
        global $wpdb;
        
        $stats = [];
        
        // Total de anotações
        $stats['total'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        
        // Usuários ativos (com pelo menos uma anotação)
        $stats['active_users'] = $wpdb->get_var("SELECT COUNT(DISTINCT user_id) FROM {$this->table_name}");
        
        // Lições com anotações
        $stats['lessons_with_annotations'] = $wpdb->get_var("SELECT COUNT(DISTINCT lesson_id) FROM {$this->table_name}");
        
        return $stats;
    }
    
    private function render_annotations_table() {
        global $wpdb;
        
        $annotations = $wpdb->get_results("
            SELECT a.*, u.display_name, p.post_title as lesson_title
            FROM {$this->table_name} a
            LEFT JOIN {$wpdb->users} u ON a.user_id = u.ID
            LEFT JOIN {$wpdb->posts} p ON a.lesson_id = p.ID
            ORDER BY a.created_at DESC
            LIMIT 50
        ");
        
        if (empty($annotations)) {
            echo '<p>Nenhuma anotação encontrada.</p>';
            return;
        }
        
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Usuário</th>
                    <th>Lição</th>
                    <th>Tempo</th>
                    <th>Anotação</th>
                    <th>Data</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($annotations as $annotation): ?>
                <tr>
                    <td><?php echo esc_html($annotation->display_name); ?></td>
                    <td><?php echo esc_html($annotation->lesson_title); ?></td>
                    <td><?php echo $this->format_time($annotation->time_seconds); ?></td>
                    <td><?php echo esc_html(wp_trim_words($annotation->text, 10)); ?></td>
                    <td><?php echo date('d/m/Y H:i', strtotime($annotation->created_at)); ?></td>
                    <td>
                        <button class="button button-small view-annotation" data-id="<?php echo $annotation->id; ?>">
                            Ver
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }
    
    public function export_annotations() {
        check_admin_referer('export_annotations');
        
        if (!current_user_can('manage_options')) {
            wp_die('Acesso negado');
        }
        
        global $wpdb;
        
        $annotations = $wpdb->get_results("
            SELECT a.*, u.display_name, u.user_email, p.post_title as lesson_title
            FROM {$this->table_name} a
            LEFT JOIN {$wpdb->users} u ON a.user_id = u.ID
            LEFT JOIN {$wpdb->posts} p ON a.lesson_id = p.ID
            ORDER BY a.created_at DESC
        ");
        
        // Configurar headers para download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=anotacoes-video-' . date('Y-m-d') . '.csv');
        
        // Criar arquivo CSV
        $output = fopen('php://output', 'w');
        
        // Cabeçalhos
        fputcsv($output, [
            'ID',
            'Usuário',
            'Email',
            'Lição',
            'Tempo (segundos)',
            'Tempo (formatado)',
            'Anotação',
            'Data de Criação'
        ]);
        
        // Dados
        foreach ($annotations as $annotation) {
            fputcsv($output, [
                $annotation->id,
                $annotation->display_name,
                $annotation->user_email,
                $annotation->lesson_title,
                $annotation->time_seconds,
                $this->format_time($annotation->time_seconds),
                $annotation->text,
                $annotation->created_at
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    private function format_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = floor($seconds % 60);
        
        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $secs);
        }
        return sprintf('%d:%02d', $minutes, $secs);
    }
    
    // Método para obter anotações de uma lição específica (usado pelo player)
    public function get_lesson_annotations($lesson_id, $user_id) {
        global $wpdb;
        
        return $wpdb->get_results($wpdb->prepare(
            "SELECT id, time_seconds, text, created_at 
             FROM {$this->table_name} 
             WHERE lesson_id = %d AND user_id = %d 
             ORDER BY time_seconds ASC",
            $lesson_id,
            $user_id
        ));
    }
}

// Inicializar a classe
MemberoVideoAnnotations::get_instance();
