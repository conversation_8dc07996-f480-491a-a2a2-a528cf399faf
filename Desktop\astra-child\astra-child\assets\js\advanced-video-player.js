/**
 * Membero Advanced Video Player
 * Sistema avançado de player de vídeo com recursos DRM
 */

class MemberoAdvancedPlayer {
    constructor() {
        this.player = null;
        this.isTheaterMode = false;
        this.annotations = [];
        this.chapters = [];
        this.currentAnnotation = null;
        this.screenRecordingDetector = null;
        this.tabVisibilityDetector = null;
        
        this.init();
    }
    
    init() {
        // Aguardar o DOM estar pronto
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initPlayer());
        } else {
            this.initPlayer();
        }
    }
    
    initPlayer() {
        const videoElement = document.querySelector('.tutorPlayer');
        if (!videoElement) {
            console.log('Player de vídeo não encontrado');
            return;
        }
        
        // Configurar Plyr
        this.setupPlyr(videoElement);
        
        // Inicializar componentes
        this.initControls();
        this.initChapters();
        this.initAnnotations();
        this.initSecurityFeatures();
        this.loadUserSettings();
        
        console.log('Membero Advanced Player inicializado');
    }
    
    setupPlyr(videoElement) {
        const plyrConfig = {
            controls: [
                'play-large',
                'play',
                'progress',
                'current-time',
                'duration',
                'mute',
                'volume',
                'settings',
                'fullscreen'
            ],
            settings: ['quality', 'speed'],
            speed: {
                selected: 1,
                options: [0.5, 0.75, 1, 1.25, 1.5, 2]
            },
            quality: {
                default: 'auto',
                options: ['auto', '1080p', '720p', '480p', '360p']
            },
            keyboard: {
                focused: true,
                global: true
            },
            tooltips: {
                controls: true,
                seek: true
            }
        };
        
        this.player = new Plyr(videoElement, plyrConfig);
        
        // Event listeners do player
        this.setupPlayerEvents();
    }
    
    setupPlayerEvents() {
        if (!this.player) return;
        
        // Salvar progresso e configurações
        this.player.on('timeupdate', () => {
            this.saveProgress();
            this.checkChapterProgress();
        });
        
        this.player.on('ratechange', () => {
            this.saveUserSettings();
        });
        
        this.player.on('volumechange', () => {
            this.saveUserSettings();
        });
        
        // Eventos de segurança
        this.player.on('play', () => {
            this.startSecurityMonitoring();
        });
        
        this.player.on('pause', () => {
            this.pauseSecurityMonitoring();
        });
    }
    
    initControls() {
        // Modo teatro
        const theaterBtn = document.getElementById('membero-theater-mode');
        if (theaterBtn) {
            theaterBtn.addEventListener('click', () => this.toggleTheaterMode());
        }
        
        // Controle de velocidade personalizado
        const speedSelect = document.getElementById('membero-speed-select');
        if (speedSelect) {
            speedSelect.addEventListener('change', (e) => {
                const speed = parseFloat(e.target.value);
                if (this.player) {
                    this.player.speed = speed;
                }
            });
        }
        
        // Toggle painéis
        const chaptersBtn = document.getElementById('membero-chapters-toggle');
        if (chaptersBtn) {
            chaptersBtn.addEventListener('click', () => this.togglePanel('chapters'));
        }
        
        const annotationsBtn = document.getElementById('membero-annotations-toggle');
        if (annotationsBtn) {
            annotationsBtn.addEventListener('click', () => this.togglePanel('annotations'));
        }
    }
    
    toggleTheaterMode() {
        const videoContainer = document.querySelector('.tutor-video-player');
        const theaterBtn = document.getElementById('membero-theater-mode');
        
        if (!videoContainer || !theaterBtn) return;
        
        this.isTheaterMode = !this.isTheaterMode;
        
        if (this.isTheaterMode) {
            videoContainer.classList.add('membero-theater-mode');
            theaterBtn.innerHTML = '<i class="fas fa-compress"></i>';
            theaterBtn.title = 'Sair do Modo Teatro';
            document.body.style.overflow = 'hidden';
        } else {
            videoContainer.classList.remove('membero-theater-mode');
            theaterBtn.innerHTML = '<i class="fas fa-expand"></i>';
            theaterBtn.title = 'Modo Teatro';
            document.body.style.overflow = '';
        }
        
        // Redimensionar player
        if (this.player) {
            setTimeout(() => {
                this.player.media.dispatchEvent(new Event('resize'));
            }, 100);
        }
    }
    
    togglePanel(panelType) {
        const panel = document.getElementById(`membero-${panelType}-panel`);
        const btn = document.getElementById(`membero-${panelType}-toggle`);
        
        if (!panel || !btn) return;
        
        const isVisible = panel.style.display !== 'none';
        
        // Fechar outros painéis
        document.querySelectorAll('.membero-panel').forEach(p => {
            if (p !== panel) {
                p.style.display = 'none';
            }
        });
        
        document.querySelectorAll('.membero-control-btn').forEach(b => {
            if (b !== btn) {
                b.classList.remove('active');
            }
        });
        
        // Toggle painel atual
        if (isVisible) {
            panel.style.display = 'none';
            btn.classList.remove('active');
        } else {
            panel.style.display = 'block';
            btn.classList.add('active');
        }
    }
    
    initChapters() {
        if (!memberoPlayer.chapters || memberoPlayer.chapters.length === 0) {
            return;
        }
        
        this.chapters = memberoPlayer.chapters;
        this.renderChapters();
    }
    
    renderChapters() {
        const chaptersList = document.getElementById('membero-chapters-list');
        if (!chaptersList) return;
        
        chaptersList.innerHTML = '';
        
        this.chapters.forEach((chapter, index) => {
            const chapterElement = document.createElement('div');
            chapterElement.className = 'membero-chapter-item';
            chapterElement.innerHTML = `
                <span class="membero-chapter-time">${this.formatTime(chapter.time)}</span>
                <span class="membero-chapter-title">${chapter.title}</span>
            `;
            
            chapterElement.addEventListener('click', () => {
                if (this.player) {
                    this.player.currentTime = chapter.time;
                    this.updateActiveChapter(index);
                }
            });
            
            chaptersList.appendChild(chapterElement);
        });
    }
    
    checkChapterProgress() {
        if (!this.player || this.chapters.length === 0) return;
        
        const currentTime = this.player.currentTime;
        let activeChapterIndex = -1;
        
        for (let i = this.chapters.length - 1; i >= 0; i--) {
            if (currentTime >= this.chapters[i].time) {
                activeChapterIndex = i;
                break;
            }
        }
        
        this.updateActiveChapter(activeChapterIndex);
    }
    
    updateActiveChapter(index) {
        document.querySelectorAll('.membero-chapter-item').forEach((item, i) => {
            if (i === index) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    initAnnotations() {
        this.annotations = memberoPlayer.annotations || [];
        this.renderAnnotations();
        this.setupAnnotationEvents();
    }
    
    setupAnnotationEvents() {
        // Botão adicionar anotação
        const addBtn = document.getElementById('membero-add-annotation');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAnnotationModal());
        }
        
        // Modal events
        const saveBtn = document.getElementById('membero-save-annotation');
        const cancelBtn = document.getElementById('membero-cancel-annotation');
        const modal = document.getElementById('membero-annotation-modal');
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveAnnotation());
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideAnnotationModal());
        }
        
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideAnnotationModal();
                }
            });
        }
    }
    
    showAnnotationModal() {
        if (!this.player) return;
        
        const modal = document.getElementById('membero-annotation-modal');
        const timeSpan = document.getElementById('membero-annotation-time');
        const textarea = document.getElementById('membero-annotation-text');
        
        if (!modal || !timeSpan || !textarea) return;
        
        const currentTime = this.player.currentTime;
        timeSpan.textContent = this.formatTime(currentTime);
        textarea.value = '';
        modal.style.display = 'flex';
        textarea.focus();
        
        this.currentAnnotation = { time: currentTime };
    }
    
    hideAnnotationModal() {
        const modal = document.getElementById('membero-annotation-modal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentAnnotation = null;
    }
    
    saveAnnotation() {
        const textarea = document.getElementById('membero-annotation-text');
        if (!textarea || !this.currentAnnotation) return;
        
        const text = textarea.value.trim();
        if (!text) return;
        
        const annotation = {
            time_seconds: this.currentAnnotation.time,
            text: text,
            created_at: new Date().toISOString()
        };
        
        // Salvar via AJAX
        this.saveAnnotationToServer(annotation);
        this.hideAnnotationModal();
    }
    
    saveAnnotationToServer(annotation) {
        jQuery.ajax({
            url: memberoPlayer.ajaxUrl,
            type: 'POST',
            data: {
                action: 'save_video_annotation',
                nonce: memberoPlayer.nonce,
                lesson_id: memberoPlayer.lessonId,
                time_seconds: annotation.time_seconds,
                text: annotation.text
            },
            success: (response) => {
                if (response.success) {
                    this.annotations.push({
                        ...annotation,
                        id: response.data.id
                    });
                    this.renderAnnotations();
                }
            },
            error: (xhr, status, error) => {
                console.error('Erro ao salvar anotação:', error);
            }
        });
    }
    
    renderAnnotations() {
        const annotationsList = document.getElementById('membero-annotations-list');
        if (!annotationsList) return;
        
        annotationsList.innerHTML = '';
        
        this.annotations.forEach((annotation) => {
            const annotationElement = document.createElement('div');
            annotationElement.className = 'membero-annotation-item';
            annotationElement.innerHTML = `
                <div class="membero-annotation-header">
                    <span class="membero-annotation-time" data-time="${annotation.time_seconds}">
                        ${this.formatTime(annotation.time_seconds)}
                    </span>
                    <div class="membero-annotation-actions">
                        <button class="membero-annotation-btn delete" data-id="${annotation.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="membero-annotation-text">${annotation.text}</div>
            `;
            
            // Event listeners
            const timeBtn = annotationElement.querySelector('.membero-annotation-time');
            if (timeBtn) {
                timeBtn.addEventListener('click', () => {
                    if (this.player) {
                        this.player.currentTime = annotation.time_seconds;
                    }
                });
            }
            
            const deleteBtn = annotationElement.querySelector('.delete');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => {
                    this.deleteAnnotation(annotation.id);
                });
            }
            
            annotationsList.appendChild(annotationElement);
        });
    }
    
    deleteAnnotation(annotationId) {
        if (!confirm('Deseja excluir esta anotação?')) return;
        
        jQuery.ajax({
            url: memberoPlayer.ajaxUrl,
            type: 'POST',
            data: {
                action: 'delete_video_annotation',
                nonce: memberoPlayer.nonce,
                annotation_id: annotationId
            },
            success: (response) => {
                if (response.success) {
                    this.annotations = this.annotations.filter(a => a.id != annotationId);
                    this.renderAnnotations();
                }
            }
        });
    }
    
    initSecurityFeatures() {
        if (!memberoPlayer.screenRecordingDetection) return;
        
        this.initScreenRecordingDetection();
        this.initTabVisibilityDetection();
    }
    
    initScreenRecordingDetection() {
        // Detectar mudanças na tela que podem indicar gravação
        this.screenRecordingDetector = setInterval(() => {
            this.checkForScreenRecording();
        }, 2000);
    }
    
    checkForScreenRecording() {
        // Verificar se há softwares de gravação conhecidos
        const suspiciousPatterns = [
            'OBS', 'Bandicam', 'Camtasia', 'ScreenFlow', 'Snagit',
            'QuickTime', 'Loom', 'CloudApp', 'Screencastify'
        ];
        
        // Verificar título da janela e user agent
        const userAgent = navigator.userAgent;
        const hasRecordingSoftware = suspiciousPatterns.some(pattern => 
            userAgent.includes(pattern) || document.title.includes(pattern)
        );
        
        // Verificar APIs de captura de tela
        if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
            // Tentar detectar se a captura de tela está ativa
            this.detectActiveScreenCapture();
        }
        
        if (hasRecordingSoftware) {
            this.handleScreenRecordingDetected('software_detection');
        }
    }
    
    detectActiveScreenCapture() {
        // Verificar se há streams de captura ativos
        if (window.screen && window.screen.captureStream) {
            try {
                const stream = window.screen.captureStream();
                if (stream && stream.active) {
                    this.handleScreenRecordingDetected('screen_capture_api');
                }
            } catch (e) {
                // Captura pode estar bloqueada ou não suportada
            }
        }
    }
    
    handleScreenRecordingDetected(detectionType) {
        // Pausar vídeo
        if (this.player && !this.player.paused) {
            this.player.pause();
        }
        
        // Mostrar aviso
        this.showRecordingWarning();
        
        // Reportar ao servidor
        this.reportScreenRecording(detectionType);
    }
    
    showRecordingWarning() {
        const warning = document.getElementById('membero-recording-warning');
        if (warning) {
            warning.style.display = 'flex';
            
            // Auto-hide após 5 segundos
            setTimeout(() => {
                warning.style.display = 'none';
            }, 5000);
        }
    }
    
    reportScreenRecording(detectionType) {
        jQuery.ajax({
            url: memberoPlayer.ajaxUrl,
            type: 'POST',
            data: {
                action: 'report_screen_recording',
                nonce: memberoPlayer.nonce,
                lesson_id: memberoPlayer.lessonId,
                detection_type: detectionType
            }
        });
    }
    
    initTabVisibilityDetection() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Usuário mudou de aba
                if (this.player && !this.player.paused) {
                    this.player.pause();
                }
            }
        });
    }
    
    startSecurityMonitoring() {
        if (!this.screenRecordingDetector) {
            this.initScreenRecordingDetection();
        }
    }
    
    pauseSecurityMonitoring() {
        // Manter monitoramento ativo mesmo quando pausado
    }
    
    saveProgress() {
        if (!this.player) return;
        
        const progress = {
            currentTime: this.player.currentTime,
            duration: this.player.duration,
            percentage: (this.player.currentTime / this.player.duration) * 100
        };
        
        // Salvar progresso localmente
        localStorage.setItem(`membero_progress_${memberoPlayer.lessonId}`, JSON.stringify(progress));
    }
    
    loadUserSettings() {
        const settings = memberoPlayer.userSettings;
        if (!settings || !this.player) return;
        
        // Aplicar configurações salvas
        if (settings.speed) {
            this.player.speed = settings.speed;
            const speedSelect = document.getElementById('membero-speed-select');
            if (speedSelect) {
                speedSelect.value = settings.speed;
            }
        }
        
        if (settings.volume) {
            this.player.volume = settings.volume;
        }
    }
    
    saveUserSettings() {
        if (!this.player) return;
        
        const settings = {
            speed: this.player.speed,
            volume: this.player.volume,
            theater_mode: this.isTheaterMode
        };
        
        jQuery.ajax({
            url: memberoPlayer.ajaxUrl,
            type: 'POST',
            data: {
                action: 'save_player_settings',
                nonce: memberoPlayer.nonce,
                ...settings
            }
        });
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
    
    destroy() {
        if (this.screenRecordingDetector) {
            clearInterval(this.screenRecordingDetector);
        }
        
        if (this.player) {
            this.player.destroy();
        }
    }
}

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    if (typeof memberoPlayer !== 'undefined') {
        window.memberoPlayerInstance = new MemberoAdvancedPlayer();
    }
});
